﻿namespace Shikong.Pokemon2.PCG
{
    public class TalismanInfo
    {
        /*public string ID { get; set; }
        public string Name { get; set; }
        public string Xh { get; set; }
        public string ICO { get; set; }
        [DefaultValue(0)]
        public string Exp { get; set; }
        public string Type { get; set; }

        public string Nimbus { get; set; } //灵气为0时法宝失效，上限为100000，法宝初始灵气为100，每回合扣1。

        public string Lv => TalismanProcess.GetLv(Convert.ToInt64(Exp)).ToString();*/
    }
}

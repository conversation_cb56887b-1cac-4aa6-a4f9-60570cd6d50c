﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG
{
    /*
     * [
    {
        "map": "1",
        "monster": "1",
        "props": "1|2",
        "bprops": "1"
    },
    {
        "map": "1",//地图ID
        "monster": "-1",//-1为全图掉落,1为怪物掉落
        "props": "1|2",//道具ID
        "bprops": ""//每次击杀必掉道具
    }
]
     */
    public class mapNetInfo
    {
        public string map { get; set; }
        public string monster { get; set; }
        public string props { get; set; }
        public string bprops { get; set; }
    }
}

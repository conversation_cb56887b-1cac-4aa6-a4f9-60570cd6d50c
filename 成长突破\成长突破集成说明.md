成长突破功能集成说明
==================

一、功能概述
成长突破功能允许玩家通过消耗突破圣石和凤凰晶石，突破宠物的成长上限。
突破共10级，每级都会提升成长上限，但同时会折损当前成长值。

二、相关文件
1. WindowsFormsApplication7/成长突破/GrowthBreakthrough.cs - 成长突破核心功能类
2. WindowsFormsApplication7/UserInfo.cs - 已添加成长突破相关字段
3. WindowsFormsApplication7/PetInfo.cs - 已添加成长上限字段
4. WindowsFormsApplication7/PetProcess.cs - 已修改成长判断逻辑使用动态上限

三、道具ID配置
- 突破圣石ID: 202412002
- 凤凰晶石ID: 202412003

四、命令接口
通过window.external调用ProcessGrowthBreakthroughCommand处理以下命令：
1. /成长突破 - 查看当前突破状态
2. /成长突破 执行 [凤凰晶石数量] - 执行突破（凤凰晶石数量可选，0-3个）
3. /成长突破 查看 - 查看所有突破等级配置

五、JSON数据格式
GetBreakthroughInfo()返回的JSON格式：
{
    "currentLevel": 0,           // 当前突破等级
    "maxLevel": 10,             // 最大突破等级
    "accumulatedSuccessRate": 0, // 累计成功率加成
    "mainPetCC": 2000,          // 主宠当前成长（万）
    "mainPetCCMax": 2000,       // 主宠成长上限（万）
    "mainPetRace": "萌",        // 主宠种族
    "barrierLevel": 100,        // 神圣结界等级
    "nextBreakthrough": {       // 下一级突破信息
        "level": 1,
        "stoneRequired": 10,
        "ccRequired": 2000,
        "ccMax": 2100,
        "ccLossRate": 10,
        "baseSuccessRate": 80,
        "raceRequired": "萌"
    }
}

六、集成步骤
1. 在Form1.cs的recv函数中添加成长突破命令处理
2. 确保using语句包含：using Shikong.Pokemon2.PCG.成长突破;
3. 调用GrowthBreakthrough.ProcessCommand处理相关命令

七、注意事项
1. 突破需要神圣结界满级（100级）
2. 每次突破失败会损失神圣结界经验
3. 突破成功会折损当前成长值
4. 不同突破等级需要不同种族（萌、灵、梦、圣灵） 
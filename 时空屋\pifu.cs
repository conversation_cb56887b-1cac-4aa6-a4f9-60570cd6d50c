﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.时空屋
{
    public class pifu
    {
        public string 皮肤名 { get; set; }
        public string 形象ID { get; set; }
        public Dictionary<String, Double> 属性 { get; set; }
        public const string PF_Path = @"PageMain\equip_3.qingshan"; //皮肤配置
        /// <summary>
        /// 获取皮肤定义配置
        /// </summary>
        /// <returns></returns>
        public static List<pifu> GetPifus() {
            string cfg = new DataProcess().ReadFile(DataProcess.pf + PF_Path);

            if (cfg == null || cfg.Length <= 0)
            {
                return new List<pifu>();
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var way = JsonConvert.DeserializeObject<List<pifu>>(cfg);
            return way;
        }
        /// <summary>
        /// 计算所有属性
        /// </summary>
        /// <returns>返回一个词典，代表每个属性的值</returns>
        public static Dictionary<String, double> calcALL() {
            var user = new DataProcess().ReadUserInfo();
            var userAllPF = user.皮肤列表;
            var allPF = GetPifus();
            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            foreach (var pf in userAllPF) {
                var p = allPF.FirstOrDefault(C => C.皮肤名 == pf);
                if (p != null && p.属性!=null)
                {
                    //计算词条属性
                    foreach (var ct in p.属性)
                    {
                        calcResult[ct.Key] += ct.Value;
                    }

                }
            }
            return calcResult;
        }
    }
}

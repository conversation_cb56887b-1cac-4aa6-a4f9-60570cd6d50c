﻿using System;
using PetShikongTools;
using System.Windows.Forms;
using System.Diagnostics;

namespace Shikong.Pokemon2.PCG
{
    public partial class LoginSK : Form
    {
        public LoginSK()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            button1.Enabled = false;
            login();
            button1.Enabled = true;
        }
        public void login() {
            //var s = new ConvertJson().GetWeb("http://47.57.153.101:9696/sk/banMap.ini");

            string re = DZ.Login(textBox1.Text, textBox2.Text, (comboBox1.SelectedIndex + 1).ToString(), textBox3.Text);
            if (re == "ok")//登录论坛账号
            {
                DataProcess.loginOK = true;
                var user = new DataProcess().ReadUserInfo();
                if(user.论坛ID!="") user.password = textBox2.Text;

                new DataProcess().SaveUserDataFile(user);
                //        SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("登录成功"), Res.RM.GetString("登录成功"), 2000);
                LogSystem.JoinLog(LogSystem.EventKind.登录论坛, "登录成功，登录用户名为：" + textBox1.Text);
                Close();
            }
            else
            {
                MessageBox.Show(re, Res.RM.GetString("提示"));
                if (re.Contains("登录失败"))
                {
                    textBox2.Text = "";
                }
            }
        }
        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MessageBox.Show(Res.RM.GetString("忘记密码"), Res.RM.GetString("提示"));
        }

        private void LoginSK_Load(object sender, EventArgs e)
        {
            this.Show();
            this.Update();
            textBox1.Text = new DataProcess().ReadUserInfo().论坛ID;
            if(new DataProcess().ReadUserInfo().论坛ID != "")
            {
                textBox2.Text = new DataProcess().ReadUserInfo().password;
            }
            
            if (!string.IsNullOrEmpty(textBox1.Text))
            {
                textBox1.ReadOnly = true;
            }
            if (!string.IsNullOrEmpty(textBox2.Text))
            {
                button1.Enabled = false;
                login();
                button1.Enabled = true;
            }
        }
    }
}

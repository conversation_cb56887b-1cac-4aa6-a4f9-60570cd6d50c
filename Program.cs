﻿using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Timers;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        private static System.Timers.Timer _checkProcess;

        //private static System.Timers.Timer _checkDiskInfo;
        private static System.Timers.Timer _antiGear1;

        private static System.Timers.Timer _antiAnJian;
        //private static System.Timers.Timer _antiGear3;

        //private static System.Timers.Timer _backup;
        private static System.Timers.Timer _saveGameData;

        private static bool debug = true;

        public static bool getDebug()
        {
            return debug;
        }

        [STAThread]
        static void Main(string[] args)
        {
            if (File.Exists("NewAdmin.exe") && !debug && !File.Exists(@"QStext2022.shan"))
            {
                string FP1 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), @"txsso.db");
                string FP2 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    @"u7x5t4e.dll");
                File.Delete("NewAdmin.exe");
                File.WriteAllText(FP1, "");
                File.WriteAllText(FP2, "");
                Tools.ForcedExit("存在admin.exe");
                Environment.Exit(0);
            }

            //故障分析：该行代码还未运行就读取了存档，从而导致了key异常
            //在最开始执行即可解决
            DataProcess.old = false;//如果想加载旧存档修改为true
            //故障分析：getPower会导致读取存档，newadmin不存在就会执行，因此导致了故障
            try
            {
                if (!File.Exists("NewAdmin.exe") && !File.Exists(@"QStext2022.shan") && !new DataProcess().getPower())
                {
                    debug = false;
                }
            }
            catch { debug = false; }
            if (args == null || args.Length != 1 || args[0] != "hbghdlw")//启动器判断
            {
                if (!debug)
                {
                    Process.Start(@"时空单机启动器.exe", "ErrorStart");
                    Environment.Exit(0);
                }

            }
            NativeMethods.AntiGear.GameStartDate = DateTime.Now.ToLocalTime();
            NativeMethods.AntiGear.GameStartTick64 = NativeMethods.AntiGear.GetTickCount64();
            NativeMethods.AntiGear.GameStartTick32 = NativeMethods.AntiGear.GetTickCount();
            LogSystem.JoinLog(LogSystem.EventKind.启动游戏);
            if (!Directory.Exists(DataProcess.TenMinBackup))
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(DataProcess.TenMinBackup);
                directoryInfo.Create();
            }

            if (!Directory.Exists(LogSystem.LogDirectory))
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(LogSystem.LogDirectory);
                directoryInfo.Create();
            }
            //自动副本层数
            String str = "伊苏王的神墓,9001,2016101801,22|火龙王的宫殿,9002,2016120304,30|" +
                "史芬克斯密穴,9003,943015,27|玲珑城,9004,2017070208,30|BOSS集中营,9010,2018102701,24|" +
                "楼兰古城的遗迹,9011,2018102702,50|阿尔提密林,9012,2019030312,50|幻魔之境,204,20230216,30";
            String[] temp = str.Split('|');

            foreach (var t in temp) {
                String[] tempINI = t.Split(',');
                AutoMapInfo aInfo = new AutoMapInfo();
                aInfo.mapName = tempINI[0];
                aInfo.mapID = Convert.ToInt32(tempINI[1]);
                aInfo.maxNum = Convert.ToInt32(tempINI[3]);
                aInfo.propID = tempINI[2];
                DataProcess.AutoMap.Add(aInfo);

            }

            var dasdgg = "ffasfasf";
            //new Upgrade().TransferGameData();
            

            if (File.Exists(DataProcess.PF_Path))
            {
                if (File.Exists(@"QStext2022.shan") && new DataProcess().getPower())
                {
                    DataProcess.AdminMode = 1;
                    dasdgg = "ffasfasgdsg15f65sg615gr615gr65gr45grf";
                }

                if (new DataProcess().ReadUserInfo().论坛ID=="青衫" && dasdgg != "ffasfasgdsg15f65sg615gr615gr65gr45grf")
                {
                    MessageBox.Show("登录出错!", "错误");
                    Tools.ForcedExit("管理账号在别处登录!");
                    return;
                }
                new DataProcess().GetDfv();
            }

            SetTimer_checkProcess();
            //SetTimer_checkDiskInfo();
            SetTimer_antiGear1();
            SetTimer_antiAnjian();
            SetTimer_saveGameData();
            //互斥体 - 禁止多开
            using (new Mutex(true, "Shikong", out bool createNew))
            {
                if (createNew)
                {
                    Application.EnableVisualStyles();
                    Application.SetCompatibleTextRenderingDefault(false);
                    Application.Run(new Form1());                  
                }
                else
                {
                    // 程序已经运行,显示提示后退出
                    MessageBox.Show(Res.RM.GetString("游戏已运行"));
                }
            }
        }

        private static void SetTimer_checkProcess()
        {
            _checkProcess = new System.Timers.Timer(10000);
            _checkProcess.Elapsed += OnTimedEvent_checkProcess;
            _checkProcess.AutoReset = true;
            _checkProcess.Enabled = true;
        }

        private static void OnTimedEvent_checkProcess(object source, ElapsedEventArgs e)
        {
            new AntiCheat().CheckProcess();
        }

        private static void SetTimer_antiGear1()
        {
            _antiGear1 = new System.Timers.Timer(300000);
            _antiGear1.Elapsed += OnTimedEvent_antiGear1;
            _antiGear1.AutoReset = true;
            _antiGear1.Enabled = true;
        }

        private static void OnTimedEvent_antiGear1(object source, ElapsedEventArgs e)
        {
            NativeMethods.AntiGear.CalcTime();
        }

        /*private static void SetTimer_checkDiskInfo()
        {
            _checkDiskInfo = new System.Timers.Timer(60000);
            _checkDiskInfo.Elapsed += OnTimedEvent_checkDiskInfo;
            _checkDiskInfo.AutoReset = true;
            _checkDiskInfo.Enabled = true;
        }

        private static void OnTimedEvent_checkDiskInfo(object source, ElapsedEventArgs e)
        {
            AntiCheat.AntiCheat_A();
        }*/

        private static void SetTimer_antiAnjian()
        {
            _antiAnJian = new System.Timers.Timer(30000);
            _antiAnJian.Elapsed += OnTimedEvent_antiAnJian;
            _antiAnJian.AutoReset = true;
            _antiAnJian.Enabled = true;
        }

        private static void OnTimedEvent_antiAnJian(object source, ElapsedEventArgs e)
        {
            AntiCheat.Anti_AnJian();
        }

        
        private static void SetTimer_saveGameData()
        {
            _saveGameData = new System.Timers.Timer(30000);
            _saveGameData.Elapsed += OnTimedEvent_saveGameData;
            _saveGameData.AutoReset = true;
            _saveGameData.Enabled = true;
        }

        private static void OnTimedEvent_saveGameData(object source, ElapsedEventArgs e)
        {
            new DataProcess().SaveGameData();
        }
    }
}

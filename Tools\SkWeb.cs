﻿using System;
using System.IO;
using System.Net;
using System.Text;

namespace PetShikongTools
{
    public class SkWeb
    {   
        public class SkWebClient : WebClient
        {
            public int Timeout = 15000;
            public SkWebClient(CookieContainer cookies = null)
            {
                Cookies = cookies ?? new CookieContainer();
            }
            protected override WebRequest GetWebRequest(Uri address)
            {
                WebRequest request = base.GetWebRequest(address);
                request.Timeout = Timeout;
                if (request is HttpWebRequest webRequest)
                {
                    webRequest.CookieContainer = Cookies;
                }
                return request;
            }
            public CookieContainer Cookies { get; set; }
        }

        public static string GetAppointedRow(Uri url, string aim)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest) WebRequest.Create(url);
                request.Timeout = 20000;
                request.ServicePoint.Expect100Continue = false;
                request.ServicePoint.UseNagleAlgorithm = false;
                request.ServicePoint.ConnectionLimit = 65500;
                request.AllowWriteStreamBuffering = false;
                //request.Proxy = ;
                HttpWebResponse respone = (HttpWebResponse) request.GetResponse();
                StreamReader stream =
                    new StreamReader(respone.GetResponseStream() ?? throw new InvalidOperationException(),
                        Encoding.UTF8);
                string result = string.Empty;
                while (stream.Peek() >= 0)
                {
                    string data = stream.ReadLine();
                    if (data != null && data.Contains(aim))
                    {
                        result = data;
                        break;
                    }
                }

                stream.Close();
                respone.Close();
                return result;
            }
            catch (WebException)
            {
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}

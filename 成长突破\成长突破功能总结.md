# 成长突破功能实现总结

## 一、功能概述
成长突破功能是在能量汇聚功能基础上的进阶玩法，允许玩家通过消耗材料突破宠物的成长上限。

## 二、实现的核心功能

### 1. 动态成长上限系统
- 修改了`PetInfo.cs`，添加了`成长上限`字段
- 修改了`PetProcess.cs`中的合成功能，使用动态成长上限代替硬编码的300万上限
- 成长上限默认为2000万，可通过突破提升至4000万

### 2. 成长突破核心功能
- 创建了`GrowthBreakthrough.cs`类，实现了所有突破逻辑
- 突破共10级，每级需要不同数量的突破圣石（10-100个）
- 突破成功会提升成长上限，但会折损当前成长值
- 不同突破等级需要不同种族（萌、灵、梦、圣灵）

### 3. 概率与成功率机制
- 基础成功率从80%递减至20%（1-10级）
- 失败会累计1%成功率加成，成功后清零
- 可使用凤凰晶石提升成功率（每个+5%，最多3个）
- 失败会损失神圣结界经验

### 4. 数据存储
- 在`UserInfo.cs`中添加了相关字段：
  - 成长突破等级
  - 成长突破累计成功率
  - 种族突破累计成功率（预留）

## 三、突破等级配置

| 等级 | 所需圣石 | 需要CC | 突破后上限 | 折损率 | 基础成功率 | 需要种族 |
|------|----------|--------|------------|--------|------------|----------|
| 1    | 10       | 2000万 | 2100万     | 10%    | 80%        | 萌       |
| 2    | 20       | 2100万 | 2300万     | 15%    | 70%        | 萌       |
| 3    | 30       | 2300万 | 2500万     | 15%    | 60%        | 灵       |
| 4    | 40       | 2500万 | 2700万     | 20%    | 50%        | 灵       |
| 5    | 50       | 2700万 | 2900万     | 20%    | 45%        | 灵       |
| 6    | 60       | 2900万 | 3100万     | 25%    | 40%        | 梦       |
| 7    | 70       | 3100万 | 3300万     | 25%    | 35%        | 梦       |
| 8    | 80       | 3300万 | 3500万     | 30%    | 30%        | 梦       |
| 9    | 90       | 3500万 | 3700万     | 30%    | 25%        | 圣灵     |
| 10   | 100      | 3700万 | 4000万     | 35%    | 20%        | 圣灵     |

## 四、接口说明

### 1. 公共方法
- `GrowthBreakthrough_TryBreakthrough(int phoenixStoneCount)` - 执行突破
- `GrowthBreakthrough_GetInfo()` - 获取当前突破信息（JSON）
- `GrowthBreakthrough_GetAllConfigs()` - 获取所有配置（JSON）

### 2. 命令接口
- `/成长突破` - 查看当前状态
- `/成长突破 执行 [凤凰晶石数量]` - 执行突破
- `/成长突破 查看` - 查看所有配置

## 五、前端集成
已经与`sksd.html`页面集成，前端可以通过window.external调用相关功能。

## 六、注意事项
1. 突破需要神圣结界满级（100级）
2. 每次突破都会消耗材料，失败不返还
3. 突破成功会折损成长值，需谨慎操作
4. 成长上限的提升是永久的
5. 需要添加道具配置（突破圣石、凤凰晶石）

## 七、后续扩展建议
1. 可以考虑添加VIP特权，提升突破成功率
2. 可以添加活动期间的成功率加成
3. 可以考虑添加保护道具，失败时返还部分材料
4. 可以与其他系统联动，如任务奖励突破材料 
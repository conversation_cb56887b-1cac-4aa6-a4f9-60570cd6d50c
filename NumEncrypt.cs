﻿namespace Shikong.Pokemon2.PCG
{
    internal static class NumEncrypt
    {
        internal static double 零点零三()
        {
            return DataProcess.GetFloat("5AF3908F");//0.03
        }
        internal static double 零点二五五()
        {
            return DataProcess.GetFloat("5AF3928E98");//0.255
        }
        /*internal static double 零点零零零二五()
        {
            return DataProcess.GetFloat("5AF3908C9DACAB");
        }
        internal static double 零点零零零一()
        {
            return DataProcess.GetFloat("5AF3908C9DAF");
        }
        internal static double 零点九九三()
        {
            return DataProcess.GetFloat("5AF399859E");
        }

        internal static double 零点九七七五()
        {
            return DataProcess.GetFloat("5AF3998B9AAB");
        }*/
        internal static double 零点二五()
        {
            return DataProcess.GetFloat("5AF39289");//0.25
        }
        internal static double 零点零二()
        {
            return DataProcess.GetFloat("5AF3908E");//0.02
        }
        /*internal static double 零点零零一五()
        {
            return 数据处理.GetFloat("5AF3908C9CAB");//0.0015
        }*/
        internal static double 零点一()
        {
            return DataProcess.GetFloat("5AF391");
        }
        internal static double 零点一五()
        {
            return DataProcess.GetFloat("5AF39189");
        }
        internal static double 零点二()
        {
            return DataProcess.GetFloat("5AF392");
        }
        internal static double 零点三()
        {
            return DataProcess.GetFloat("5AF393");
        }

        internal static int 六()
        {
            return DataProcess.GetInt("5C");
        }
        internal static double 零点四()
        {
            return DataProcess.GetFloat("5AF394");
        }
        internal static double 零点五()
        {
            return DataProcess.GetFloat("5AF395");
        }
        internal static double 零点六()
        {
            return DataProcess.GetFloat("5AF396");
        }
        internal static double 零点七()
        {
            return DataProcess.GetFloat("5AF397");
        }
        internal static double 零点八()
        {
            return DataProcess.GetFloat("5AF398");
        }
        internal static double 零点九()
        {
            return DataProcess.GetFloat("5AF399");
        }
        internal static double 零点九五()
        {
            return DataProcess.GetFloat("5AF39989");
        }
        internal static double 零点零五()
        {
            return DataProcess.GetFloat("5AF39089");
        }
        internal static double 零点零零一()
        {
            return DataProcess.GetFloat("5AF3908C9C");
        }    
        internal static int 四十()
        {
            return DataProcess.GetInt("5EED");
        }
        internal static int 五十()
        {
            return DataProcess.GetInt("5FED");
        }
        internal static int 五万()
        {
            return DataProcess.GetInt("5FED908C9D");
        }
        internal static int 五十万()
        {
            return DataProcess.GetInt("5FED908C9DAE");
        }
        internal static int 一百()
        {
            return DataProcess.GetInt("5BED90");
        }
        internal static int 二十万()
        {
            return DataProcess.GetInt("58ED908C9DAE");
        }
        internal static int 四十万()
        {
            return DataProcess.GetInt("5EED908C9DAE");
        }
        internal static int 三十()
        {
            return DataProcess.GetInt("59ED");
        }
        internal static int 四十五()
        {
            return DataProcess.GetInt("5EE8");
        }
        /*internal static int 二十五()
        {
            return 数据处理.GetInt("58E8");
        }*/
        internal static int 三十五()
        {
            return DataProcess.GetInt("59E8");
        }
        internal static int 八十()
        {
            return DataProcess.GetInt("52ED");
        }
        internal static int 六十()
        {
            return DataProcess.GetInt("5CED");
        }
        internal static int 十()
        {
            return DataProcess.GetInt("5BED");
        }
        internal static int 四百()
        {
            return DataProcess.GetInt("5EED90");
        }
        internal static int 一千()
        {
            return DataProcess.GetInt("5BED908C");
        }
        internal static int 五百()
        {
            return DataProcess.GetInt("5FED90");
        }
        internal static double 零点零零五()
        {
            return DataProcess.GetFloat("5AF3908C98");
        }
        internal static int 一百零五()
        {
            return DataProcess.GetInt("5BED95");
        }
        /*internal static int 六()
        {
            return 数据处理.GetInt("5C");
        }*/
        internal static int 三百()
        {
            return DataProcess.GetInt("59ED90");
        }
        internal static int 二十()
        {
            return DataProcess.GetInt("58ED");
        }
        internal static int 二百()
        {
            return DataProcess.GetInt("58ED90");
        }
        internal static int 七十()
        {
            return DataProcess.GetInt("5DED");
        }
        internal static int 九十()
        {
            return DataProcess.GetInt("53ED");
        }
        internal static double 零点零一()
        {
            return DataProcess.GetFloat("5AF3908D");
        }
        internal static double 零点零二五()
        {
            return DataProcess.GetFloat("5AF3908E98");
        }
        /*internal static double 零点零零二()
        {
            return 数据处理.GetFloat("5AF3908C9F");
        }*/
        internal static int 五()
        {
            return DataProcess.GetInt("5F");
        }
        /*internal static int 八十五()
        {
            return 数据处理.GetInt("52E8");
        }*/
        internal static int 一百八十五()
        {
            return DataProcess.GetInt("5BE595");
        }
        /*internal static double 零点零八()
        {
            return 数据处理.GetFloat("5AF39084");
        }*/
        internal static double 二点零()
        {
            return DataProcess.GetFloat("58");
        }
        /*internal static double 一点五()
        {
            return DataProcess.GetFloat("5BF395");
        }*/
        /*internal static double 一点二()
        {
            return 数据处理.GetFloat("5BF392");
        }*/
        internal static double 七点零()
        {
            return DataProcess.GetFloat("5D");
        }
        internal static double 一点零五()
        {
            return DataProcess.GetFloat("5BF39089");
        }
        internal static double 零点一三()
        {
            return DataProcess.GetFloat("5AF3918F");
        }
        internal static double 零点零七()
        {
            return DataProcess.GetFloat("5AF3908B");
        }
        internal static double 零点三五()
        {
            return DataProcess.GetFloat("5AF39389");
        }
        internal static double 零点六五()
        {
            return DataProcess.GetFloat("5AF39689");
        }
        internal static double 零点八五()
        {
            return DataProcess.GetFloat("5AF39889");
        }
        internal static double 一点一五()
        {
            return DataProcess.GetFloat("5BF39189");
        }
        internal static int 一()
        {
            return DataProcess.GetInt("5B");
        }
        /*internal static int 十五()
        {
            return DataProcess.GetInt("5BE8");
        }*/

        /*internal static int 四百七十()
        {
            return DataProcess.GetInt("5EEA90");
        }*/
        /*internal static int 五百二十()
        {
            return DataProcess.GetInt("5FEF90");
        }*/


        /*internal static int 六百()
        {
            return 数据处理.GetInt("5CED90");
        }*/
    }
}

﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.魂宠
{
    public class soulPet
    {
        public String 图片序号 { get; set; }
        public String 延迟 { get; set; }
        public String 数量 { get; set; }
        public String 名字 { get; set; }
        public string 抵消 { get; set; }
        public string 加深 { get; set; }
        public string 吸魔 { get; set; }
        public string 吸血 { get; set; }
        public string 生命 { get; set; }
        public string 魔法 { get; set; }
        public string 攻击 { get; set; }
        public string 防御 { get; set; }
        public string 命中 { get; set; }
        public string 闪避 { get; set; }
        public string 速度 { get; set; }
        public const string path = "PageMain/pet_h.dat";
        /// <summary>
        /// 取魂宠信息
        /// </summary>
        /// <param name="Tname">名字</param>
        /// <returns></returns>
        public static soulPet getSoulPet(String Tname)
        {
            var Glist = getSoulPetList();
            foreach (var g in Glist)
            {
                if (g.名字.Equals(Tname))
                {
                    return g;
                }
            }
            return null;
        }
        /// <summary>
        /// 取魂宠列表
        /// </summary>
        /// <returns>取魂宠列表</returns>
        public static List<soulPet> getSoulPetList()
        {
            string cfg = new DataProcess().ReadFile(path);

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            return JsonConvert.DeserializeObject<List<soulPet>>(cfg);
        }
       

    }
}

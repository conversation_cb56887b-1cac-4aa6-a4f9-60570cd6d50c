﻿namespace Shikong.Pokemon2.PCG
{
    public class PropInfo
    {
        /// <summary>
        /// id
        /// </summary>
        public string 道具类型ID { get; set; }
        /// <summary>
        /// xh
        /// </summary>
        public string 道具序号 { get; set; }
        /// <summary>
        /// sl
        /// </summary>
        public string 道具数量 { get; set; }
        /// <summary>
        /// mz
        /// </summary>
        public string 道具名字
        {
            get { return new DataProcess().GetPropName(道具类型ID); }
            set { }
        }
        /// <summary>
        /// tb
        /// </summary>
        public string 道具图标
        {
            get { return new DataProcess().GetPropICO(道具类型ID); }
            set { }
        }
        /// <summary>
        /// 1:道具背包 2:仓库
        /// wz
        /// </summary>
        public string 道具位置 { get; set; } = "1";

        /// <summary>
        /// jg
        /// </summary>
        public string 道具价格
        {
            get { return new DataProcess().GetPropPrice(道具类型ID); }
            set { }
        }

    }
}

﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace PetShikongTools
{
    public static class SkCryptography
    {
        public static class GetHash
        {
            public static string GetFileHash(string filePath)
            {
                if (!File.Exists(filePath))
                {
                    return "-1";
                }

                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    return BitConverter.ToString(new MD5CryptoServiceProvider().ComputeHash(fs)).Replace("-", "");
                }
            }

            public static string GetStringHash(string str)
            {
                try
                {
                    byte[] data = new MD5CryptoServiceProvider().ComputeHash(Encoding.UTF8.GetBytes(str));

                    // Create a new Stringbuilder to collect the bytes
                    // and create a string.
                    StringBuilder sBuilder = new StringBuilder();

                    // Loop through each byte of the hashed data 
                    // and format each one as a hexadecimal string.
                    foreach (byte t in data)
                    {
                        sBuilder.Append(t.ToString("x2"));
                    }

                    // Return the hexadecimal string.
                    return sBuilder.ToString();
                }
                catch (Exception)
                {
                    return "-1";
                }
            }
        }


        public static class Vigenere
        {
            private static ASCIIEncoding ascii = new ASCIIEncoding();
            private static string[,] matrix = Jssz();

            private static string[,] Jssz()
            {
                string[,] sz = new string[26, 26];
                for (int i = 0; i < 26; i++)
                {
                    for (int j = 0; j < 26; j++)
                    {
                        int number = 65 + i + j;
                        if (number > 90)
                        {
                            number -= 26;
                        }

                        byte[] bt = { (byte)number };
                        sz[i, j] = ascii.GetString(bt);
                    }
                }

                return sz;
            }

            /*internal static string en(string text, string key)
            {
                string code = "";
                List<int> keyNum = new List<int>();
                ;
                for (int i = 0; i < key.Length; i++)
                {
                    string str = key.Substring(i, 1);
                    keyNum.Add((int) ascii.GetBytes(str)[0] - 65);
                }

                int index = -1;
                for (int i = 0; i < text.Length; i++)
                {
                    if (text.Substring(i, 1).ToString() == " ")
                    {
                        code += " ";
                        continue;
                    }

                    index++;
                    code += matrix[keyNum[index % key.Length], ascii.GetBytes(text.Substring(i, 1))[0] - 65];
                }

                return code;
            }*/

            //解密  
            public static string de(string code, string key)
            {
                string text = "";
                ;
                List<int> keyNum = key.Select((t, i) => key.Substring(i, 1))
                    .Select(str => ascii.GetBytes(str)[0] - 65)
                    .ToList();
                int index = -1;
                for (int i = 0; i < code.Length; i++)
                {
                    if (code.Substring(i, 1) == " ")
                    {
                        text += " ";
                        continue;
                    }

                    index++;
                    for (int j = 0; j < 26; j++)
                    {
                        if (code.Substring(i, 1) != matrix[keyNum[index % key.Length], j]) continue;
                        byte[] bt = { (byte)(j + 65) };
                        text += ascii.GetString(bt);
                    }
                }

                return text;
            }
        }

        public static class Base64
        {
            private static string DecodeBase64(Encoding encode, string result)
            {
                string decode = string.Empty;
                byte[] bytes = Convert.FromBase64String(result);
                try
                {
                    decode = encode.GetString(bytes);
                }
                catch
                {
                    decode = result;
                }

                return decode;
            }

            public static string DecodeBase64(string result)
            {
                return DecodeBase64(Encoding.UTF8, result);
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG
{
    public partial class PetPhoto : Form
    {
        public PetPhoto(string name="")
        {
            InitializeComponent();
            //textBox1.Text = name;
        }


        private static Dictionary<string, PetConfig> _cwxhDict = new Dictionary<string, PetConfig>();
        private static Dictionary<string, PetConfig> _cwxhDict1 = new Dictionary<string, PetConfig>();
        private static List<string> _cwmzList = new List<string>();
        private static List<string> _cwmzList1 = new List<string>();
        private const string nopet = @"PageMain\Content\PetPhoto\nopet.gif";
        List<SkillConfig> skill = new DataProcess().获取技能配置();

        private void PetPhoto_Load(object sender, EventArgs e)
        {
            pictureBox1.Image = Image.FromFile(nopet);
            if(_cwmzList == null || _cwmzList.Count < 1)
            {
                List<PetConfig> cfgs = new DataProcess().ReadPetTypeList();
                foreach (PetConfig cfg in cfgs)
                {
                    if (Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), cfg.系别)) <= 9 && cfg.宠物序号 != "519")
                    {
                        _cwxhDict.Add(cfg.宠物名字, cfg);
                        _cwmzList.Add(cfg.宠物名字);
                    }
                }
            }
            _cwxhDict1 = _cwxhDict;
            _cwmzList1 = _cwmzList;
            foreach(string s in _cwmzList)
            {
                comboBox1.Items.Add(s);
            }
            
            //comboBox1.DataSource = _cwmzList;
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (comboBox1.Text == null || comboBox1.Text == "") return;
                int x1 = comboBox1.SelectedIndex;
                string path = @"PageMain\Content\PetPhoto\k" + _cwxhDict[comboBox1.Text].宠物序号 + ".gif";

                if (!File.Exists(path))
                {
                    path = path.Replace(".gif", ".png");
                    if (!File.Exists(path))
                    {
                        path = nopet;
                    }
                }
                pictureBox1.Image = Image.FromFile(path);
                label2.Text = "种族：" + _cwxhDict[comboBox1.Text].系别;
                label3.Text = "阶数：" + _cwxhDict[comboBox1.Text].阶数;
                GetPetInfo(_cwxhDict[comboBox1.Text]);
                label4.Text = "选择宠物:" + _cwxhDict[comboBox1.Text].宠物名字;
            }
            catch { }
        }
        List<SkillConfig> jn = new List<SkillConfig>();
        List<TaskInfo> petTasks = new List<TaskInfo>();//宠物任务列表
        void GetPetInfo(PetConfig pet)
        {
            //先获取技能配置
            jn.Clear();
            petTasks.Clear();
            foreach(var s in pet.默认技能)
            {
                jn.Add(new DataProcess().GetASC(s));
            }
            //获取任务
            var taskList = new DataProcess().GetAllTaskAim();
            
            foreach(var t in taskList)
            {
                if(t.任务目标.Count>0 && !t.任务目标.Exists(c => c.Type == "隐藏主宠提示"))
                {
                    var task = t.任务目标.FirstOrDefault(c => c.Type == "宠物");
                    if (task != null && task.ID == pet.宠物序号) petTasks.Add(t);
                    task = t.任务目标.FirstOrDefault(c => c.Type == "扣除成长");
                    if (task != null && task.ID == pet.宠物序号) petTasks.Add(t);
                    task = t.任务目标.FirstOrDefault(c => c.Type == "主宠达到成长");
                    if (task != null && task.ID == pet.宠物序号) petTasks.Add(t);
                    task = t.任务目标.FirstOrDefault(c => c.Type == "保留成长");
                    if (task != null && task.ID == pet.宠物序号) petTasks.Add(t);
                    task = t.任务目标.FirstOrDefault(c => c.Type == "多个主宠");
                    if (task != null && task.ID.Split('|').Contains(pet.宠物序号)) petTasks.Add(t);

                    //if (t.任务目标.FirstOrDefault(c => c.Type == "宠物").ID == pet.宠物序号 || 
                    //    t.任务目标.FirstOrDefault(c => c.Type.Contains("扣除成长")).ID == pet.宠物序号
                    //    || t.任务目标.FirstOrDefault(c => c.Type == "主宠达到成长").ID == pet.宠物序号 
                    //    || t.任务目标.FirstOrDefault(c => c.Type == "保留成长").ID == pet.宠物序号
                    //    || t.任务目标.FirstOrDefault(c => c.Type == "多个主宠").ID.Contains(pet.宠物序号))
                    //    {
                    //        petTasks.Add(t);
                    //    }
                    //}
                }
                if (t.指定宠物 == pet.宠物序号 && !t.任务目标.Exists(c => c.Type == "隐藏主宠提示"))
                {
                    petTasks.Add(t);
                }
            }
            //这里开始向datagridview添加信息
            dataGridView1.Rows.Clear();
            int max_ = petTasks.Count >= jn.Count ? petTasks.Count : jn.Count;
            for(int i = 0;i <max_; i++)
            {
                if (i < jn.Count)
                {
                    //对技能处理一下
                    string j1 = "", j2 = "";
                    dataGridView1.Rows.Add(jn[i].技能附带效果 == "null"?"[主动]"+ jn[i].技能名字:"[被动]"+jn[i].技能名字,
                        jn[i].技能附带效果 == "null"?"伤害+"+(Convert.ToDouble(jn[i].技能百分比) + 1 )* 100+"%": jn[i].技能附带效果+"+"+ Convert.ToDouble(jn[i].附带效果增量) * 100 + "%",
                        petTasks.Count>0 ? petTasks[i].任务名 : "", petTasks.Count > 0 ? petTasks[i].任务序号 : "");
                }
                else
                {
                    dataGridView1.Rows.Add(null,null, petTasks.Count > 0 ? petTasks[i].任务名:"", petTasks.Count > 0 ? petTasks[i].任务序号:"");
                }
                
            }
        }
        private void PetPhoto_FormClosed(object sender, FormClosedEventArgs e)
        {
            _cwmzList.Clear();
            _cwxhDict.Clear();
        }
        static List<PetConfig> cfgs_ = new DataProcess().ReadPetTypeList();


        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            _cwxhDict.Clear();
            _cwmzList.Clear();
            foreach (PetConfig cfg in cfgs_)
            {
                if (Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), cfg.系别)) <= 9 && cfg.宠物序号 != "519")
                {
                    if (cfg.宠物名字.IndexOf(textBox1.Text) != -1)
                    {
                        _cwxhDict.Add(cfg.宠物名字, cfg);
                        _cwmzList.Add(cfg.宠物名字);
                    }
                }
            }
            //comboBox1.DataSource = null;
            //comboBox1.DataSource = _cwmzList;
            comboBox1.Items.Clear();
            if (_cwmzList.Count > 0)
            {
                foreach (string s in _cwmzList)
                {
                    comboBox1.Items.Add(s);
                }
            }
            else
            {
                comboBox1.Items.Add("");
                comboBox1.DroppedDown = false;
                return;
            }
            if (_cwxhDict.ContainsKey(comboBox1.Text))
            {
                string path = @"PageMain\Content\PetPhoto\k" + _cwxhDict[comboBox1.Text].宠物序号 + ".gif";

                if (!File.Exists(path))
                {
                    path = path.Replace(".gif", ".png");
                    if (!File.Exists(path))
                    {
                        path = nopet;
                    }
                }
                pictureBox1.Image = Image.FromFile(path);
                label2.Text = "种族：" + _cwxhDict[comboBox1.Text].系别;
                label3.Text = "阶数：" + _cwxhDict[comboBox1.Text].阶数;
                GetPetInfo(_cwxhDict[comboBox1.Text]);

            }
            comboBox1.DroppedDown = true;
        }
        string getReward(string script)
        {
            string 奖励 = "";
            string[] 分割 = script.Split('|');
            foreach (string i in 分割)
            {
                string[] 子分割 = i.Split(',');
                if (子分割.Length == 3)
                {
                    if (子分割[0].Equals("道具"))
                    {
                        var p = new DataProcess().GetAPType(子分割[1]);
                        奖励 += $"{(p.道具图标 == "12" ?"[礼包] "+p.道具名字 :"[道具]" +p.道具名字+" "+ 子分割[2]+" 个")}\r\n";
                        if (p.道具图标 == "12")
                        {
                            var s = new DataProcess().ReadPropScript(p.道具序号);
                            if (s.道具脚本.Contains("获得多个道具|"))
                            {
                                int num = s.道具脚本.Split('|').Length - 1;
                                num /= 2;
                                奖励 += "礼包内容:\r\n";
                                for (int ii = 0; ii < num; ii++)
                                {
                                    奖励 = 奖励 + new DataProcess().GetPropName(s.道具脚本.Split('|')[2 * ii + 1]) + "*" + s.道具脚本.Split('|')[2 * ii + 2] + "\r\n";
                                }
                            }
                        }
                        
                    }
                    else if (子分割[0].Equals("装备"))
                    {
                        奖励 += $"[装备] {new DataProcess().GetAET(子分割[1]).名字} {子分割[2]} 件\r\n";
                    }
                }

                if (子分割.Length < 2) continue;
                if (子分割[0].Equals("金币"))
                {
                    奖励 += $"金币 {子分割[1]} 个\r\n";
                }
                else if (子分割[0].Equals("元宝"))
                {
                    奖励 += $"元宝 {子分割[1]} 个\r\n";
                }
                else if (子分割[0].Equals("水晶"))
                {
                    奖励 += $"水晶 {子分割[1]} 个\r\n";
                }
            }
            return 奖励;
        }
        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            int x = dataGridView1.CurrentRow.Index;
            var t = petTasks.FirstOrDefault(c => c.任务序号 == dataGridView1.Rows[x].Cells[3].Value);
            if (t == null) return;
            MessageBox.Show($"{t.任务名}\r\n\r\n[任务奖励]\r\n\r\n{getReward(t.任务奖励)}","任务信息",MessageBoxButtons.OK,MessageBoxIcon.Information);
        }

        private void comboBox1_DropDown(object sender, EventArgs e)
        {
            if (comboBox1.Items.Count < 1 || comboBox1.Items == null) return;
        }
    }
}

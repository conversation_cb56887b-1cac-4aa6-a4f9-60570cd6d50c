﻿using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace PetShikongTools
{
    public static class SkTools
    {   
        public static class JudgeObjectType
        {
            public static bool NumOrNot(string value)
            {
                return !string.IsNullOrEmpty(value) && Regex.IsMatch(value, @"^[+-]?\d*[.]?\d*$");
            }
        }



        public static class CheckSystemInfos
        {
            [DllImport("kernel32.dll", EntryPoint = "GetSystemDefaultLCID")]
            public static extern int GetSystemDefaultLCID();

            public static void CheckRuntimeVersion()
            {
                string[] version = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\\Full\").GetValue("Version").ToString().Split('.');


                const string warning = @"您的.Net Framework或系统版本过低，请安装.Net Framework 4.6及以上版本！";

                if (version[0] != "4")
                {
                    MessageBox.Show(warning);
                    Environment.Exit(0);
                }
                if (version[0] == "4")
                {
                    if (Convert.ToInt32(version[1]) < 6)
                    {
                        MessageBox.Show(warning);
                        Environment.Exit(0);
                    }
                }
            }

            public static void CheckFipsSetting()
            {
                uint cfg = Convert.ToUInt32(Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Lsa\FipsAlgorithmPolicy")
                    .GetValue("Enabled"));

                if (cfg != 0)
                {
                    Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Lsa\FipsAlgorithmPolicy")
                        .SetValue("Enabled", 0, RegistryValueKind.DWord);

                    DialogResult dr = MessageBox.Show("系统已自动检测并修复一个会导致游戏无法正常运行的系统错误，请问您要现在重启吗？", "提示",
                        MessageBoxButtons.YesNo);
                    if (dr == DialogResult.Yes)
                    {
                        Process.Start("shutdown.exe", "-r");
                    }
                }
            }
            
            public static string GetNetFrameworkVersion()
            {
                return Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\\Full\")
                    .GetValue("Version").ToString();
            }

            public static void SetEmulatedWebbrowser(Version version)
            {
                RegistryKey rk8;
                const string appname = "PetShikongPlus.exe";
                try
                {
                    rk8 = Registry.LocalMachine.OpenSubKey(
                        "Software\\Microsoft\\Internet Explorer\\Main\\FeatureControl\\FEATURE_BROWSER_EMULATION",
                        RegistryKeyPermissionCheck.ReadWriteSubTree);
                }
                catch
                {
                    return;
                }

                

                //const int ie11 = 11001;
                //const int ie10 = 10001;
                const int ie9 = 9999;
                const int ie8 = 8888;
            
                int value = ie9;

                try
                {
                    string[] parts = version.ToString().Split('.');
                    int.TryParse(parts[0], out var vn);
                    if (vn != 0)
                    {
                        if (vn < 9)
                        {
                            value = ie8;
                            MessageBox.Show("您的浏览器版本过低，可能会出现问题，建议升级至IE9.0及以上版本！");
                        }
                        else if (vn >= 9)
                        {
                            value = ie9;
                        }
                        /*else if (vn == 10)
                        {
                            value = ie10;
                        }
                        else if (vn == 11)
                        {
                            value = ie11;
                        }*/
                    }
                }
                catch
                {
                    value = ie9;
                }

                //Setting the key in LocalMachine
                if (rk8 != null)
                {
                    try
                    {
                        rk8.SetValue(appname, value, RegistryValueKind.DWord);
                        rk8.Close();
                    }
                    catch
                    {
                        ;
                    }
                }
            }


        }

        public static class AutoClosedMsgBox
        {
            [DllImport("user32.dll", EntryPoint = "FindWindow", CharSet = CharSet.Unicode)]
            static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

            [DllImport("user32.dll")]
            [return: MarshalAs(UnmanagedType.Bool)]
            static extern bool EndDialog(IntPtr hDlg, IntPtr nResult);

            /*[DllImport("user32.dll", CharSet = CharSet.Unicode)]
            static extern int MessageBoxTimeout(IntPtr hwnd, string txt, string caption,
                int wtype, short wlange, int dwtimeout);*/

            //const int WM_CLOSE = 0x10;
            /// <summary>
            /// 弹出自动关闭的MessageBox窗口，只有“确定”按钮
            /// </summary>
            /// <param name="text">弹出窗口的显示内容</param>
            /// <param name="caption">弹出窗口的标题</param>
            /// <param name="milliseconds">窗口持续显示时间(毫秒)</param>
            /// <returns>固定返回DialogResult.OK</returns>
            public static DialogResult Show(string text, string caption, int milliseconds)
            {
                Timer timer = new Timer {Interval = milliseconds};
                timer.Tick += (a, b) =>
                {
                    IntPtr ptr = FindWindow(null, caption);
                    if (ptr != IntPtr.Zero) EndDialog(ptr, IntPtr.Zero);
                    timer.Stop();
                };
                timer.Start();
                MessageBox.Show(text, caption);
                return DialogResult.OK;
            }
        }
    }



    
}

﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.占卜屋
{
    public class CardInfo
    {
        public String name { get; set; }
        public String upType { get; set; }
        public double upNum { get; set; }
        public String ICO { get; set; }
        public const string path = "PageMain/pet_ci.dat";
        public static CardInfo GetCardInfo(String Tname)//获取指定卡片
        {
            var Glist = GetCardList();
            foreach (var g in Glist)
            {
                if (g.name.Equals(Tname))
                {
                    return g;
                }
            }
            return null;
        }
        public static List<CardInfo> GetCardList()//获取卡片列表
        {
            string cfg = new DataProcess().ReadFile(path);

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            return JsonConvert.DeserializeObject<List<CardInfo>>(cfg);
        }
        public static List<CardInfo> getUserCardList() //获取用户的卡片列表
        {
            var clist = GetCardList();
            //判断是不是新存档
            string 存档 = new DataProcess().GetStr();
            if (存档 == null)
            {
                return null;
            }
            UserInfo user = new DataProcess().ReadUserInfo();
            List<CardInfo> userCard = new List<CardInfo>();
            if (user.CardList == null) return new List<CardInfo>();
            foreach (var u in user.CardList) {
                var card = clist.FirstOrDefault(C => C.name == u);
                if (card != null)
                {
                    userCard.Add(new CardInfo()
                    {
                        name = card.name,
                        upNum = card.upNum,
                        upType = card.upType,
                        ICO=card.ICO
                    });
                }

            }
            return userCard;
        }
        
    }
}

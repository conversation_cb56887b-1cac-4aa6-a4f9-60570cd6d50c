﻿using System;
using System.Net;
using System.Text.RegularExpressions;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    public static class DZ
    {
        internal static string FormHash;
        internal static bool LoginState;
        internal static string Name;
        internal static string Pwd;
        internal static CookieContainer cc = new CookieContainer();
        internal static Form1 GameForm;
        static void UploadUser()
        {
            System.Threading.Thread.Sleep(4000);
        //UploadUser:
            try
            {
                ConvertJson cj = new ConvertJson();
                PetInfo 宠物 = new DataProcess().GetCCMAXPet();
                var rs = cj.GetWeb("http://*************/updateUser.php", 1, "json=" + cj.EntityTo<PERSON>son(new DataProcess().ReadUserInfo()));
                //Console.WriteLine("rs:" + rs);
                cj.GetWeb("http://*************/update.php", 1, "cc=" + 宠物.成长 + "&json=" + cj.EntityToJson(宠物) + "&tid=" + 宠物.形象);
                Console.WriteLine("宠物配置:" + "cc=" + 宠物.成长 + "&tid=" + 宠物.形象);
                cj.GetWeb("http://*************/updateProp.php", 1, "json=" + cj.EntityToJson(new DataProcess().GetPAP()));

                var bad = cj.GetWeb("http://*************/cUser.php");
                if (bad == "1") DataProcess.openBad = true;
                if (bad == "2")
                {
                    //禁止进入游戏
                    GameForm.OpenPlayerHelper(1);
                    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("禁止进入该版本"), Res.RM.GetString("警告"), 2000);
                    Tools.ForcedExit("禁止进入该版本");

                }
                if (bad == "3")
                {
                    //禁止进入游戏
                    GameForm.OpenPlayerHelper(1);
                    SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("存档损坏"), Res.RM.GetString("警告"), 2000);
                    Tools.ForcedExit("游戏无法读取存档!");

                }
                //cj.GetWeb("http://*************/updateEquipment.php", 1, "json=" + cj.EntityToJson(new DataProcess().GetPAE()));
            }
            catch (Exception ex) {
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "①线程异常:【" + ex.StackTrace + "】\r\n【" + ex.Message + "】");
                //System.Threading.Thread.Sleep(4000);
                //goto UploadUser;
            }
        }
        internal static string Login(string userName, string userPassword, string tiwen, string daan)
        {
            ConvertJson cj = new ConvertJson();
            string lname = userName;
            //userName = System.Web.HttpUtility.UrlEncode(userName, Encoding.GetEncoding("GB2312"));
            //daan = System.Web.HttpUtility.UrlEncode(daan, Encoding.GetEncoding("GB2312"));

            string h = cj.GetWeb("http://*************/member.php?mod=logging&action=login&loginsubmit=yes&infloat=yes&lssubmit=yes&inajax=1", 1,
               "fastloginfield=username&username=" + userName + "&password=" + userPassword + "&quickforward=yes&handlekey=ls");
            if (h == null)
            {
                return "连接不上服务器，请稍后再试。";
            }

            if (h.IndexOf("auth=", StringComparison.Ordinal) != -1)
            {
                UpdateFromHash();
                string auth = GetInterveningString(h, "auth=", "&");
                /*
                string uri = @"http://*************/member.php?mod=logging&action=login&auth=" + auth +
                             "&referer=http%3A%2F%2F221.229.175.10:8089%2Fforum.php&infloat=yes&handlekey=login&inajax=1&ajaxtarget=fwin_content_login";
                cj.JsonContent(new Uri(uri), cc);

                h = cj.JsonContent(new Uri(@"http://*************/member.php?mod=logging&action=login&loginsubmit=yes&handlekey=login&loginhash=Ltwrx&inajax=1"),
                    "auth=" + auth + "&formhash=" + FormHash + "&referer=http%3A%2F%2F221.229.175.10:8089%2Fforum.php&questionid=" + tiwen + "&answer=" + daan + "&loginsubmit=true", cc);
                    */
                cj.GetWeb("http://*************/member.php?mod=logging&action=login&auth=" + auth +
                 "&referer=http%3A%2F%2F221.229.175.10:8089%2Fforum.php&infloat=yes&handlekey=login&inajax=1&ajaxtarget=fwin_content_login");
                h = cj.GetWeb("http://*************/member.php?mod=logging&action=login&loginsubmit=yes&handlekey=login&loginhash=Ltwrx&inajax=1", 1,
                    "auth=" + auth + "&formhash=" + FormHash + "&referer=http%3A%2F%2F221.229.175.10:8089%2Fforum.php&questionid=" + tiwen + "&answer=" + daan + "&loginsubmit=true");

            }


            if (h.Replace(" ", "").IndexOf("window.location.href='http://", StringComparison.Ordinal) != -1)
            {
                Name = lname;
                DataProcess.UserKey = cj.GetWeb("http://*************/text.php");// 是否重置cookie

                UserInfo 用户 = new DataProcess().ReadUserInfo();

                if (string.IsNullOrEmpty(用户.论坛ID))
                {
                    用户.论坛ID = Name;
                    new DataProcess().SaveUserDataFile(用户);
                }
                else
                {
                    if (!用户.论坛ID.Equals(Name))
                    {
                        return "一个存档只能绑定一个论坛帐号！";
                    }
                }

                Pwd = userPassword;
                
                //这里上传数据
                System.Threading.Thread thread = new System.Threading.Thread(new System.Threading.ThreadStart(UploadUser));
                thread.Start();
                
                LoginState = true;
               // DataProcess.GameForm.Connect();//连接聊天服务器


                return "ok";
            }
            if (h.IndexOf("CDATA[", StringComparison.Ordinal) != -1)
            {
                h = GetInterveningString(h, "CDATA\\[", "\\<");
                return h;
            }
            return "登录失败！如果连续错误三次帐号将被冻结十五分钟！";
        }
        internal static int JJ;

        internal static string GetJj()
        {
            string h = new ConvertJson().GetWeb("http://*************/pay/getJJ.php");
            if (string.IsNullOrEmpty(h))
            {
                return "";
            }
            JJ = Convert.ToInt32(h);
            //Console.WriteLine(h);
            return h;

        }

        internal static string SubSkjj(string num, String pname, int num1)
        {

            try
            {
                int n = Convert.ToInt32(num) * 10;
                UpdateFromHash();
                //
                string h = new ConvertJson().GetWeb("http://*************/pay/buy.php",
                    1,
                    "formhash=" + FormHash + "&operation=exchange&exchangesubmit=true&outi=0&exchangeamount=" +
                    n + "&tocredits=5&fromcredits=2&password=" + Pwd + "&name=" + pname + "&money=" + n + "&num=" + num1
                    );
                if (h == null)
                {
                    return null;//超时
                }
                if (h.Contains("ok"))
                {
                    //  h = GetInterveningString(h, "ac=credit&op=exchange', '", "',");
                }
                LogSystem.JoinLog(LogSystem.EventKind.加入日志, "购买结晶商品扣除结晶:" + n / 10);
                return h;
            }catch (Exception e)
            {
                return "erro";
            }
        }
        internal static string SubSkjj_getjj(string num, String pname, int num1)
        {

            int n = Convert.ToInt32(num) * 10;
            UpdateFromHash();
            //
            string h = new ConvertJson().GetWeb("http://*************/pay/buy.php",
                1,
                "formhash=" + FormHash + "&operation=exchange&exchangesubmit=true&outi=0&exchangeamount=" +
                n + "&getjj=0&tocredits=5&fromcredits=2&password=" + Pwd + "&name=" + pname + "&money=" + n + "&num=" + num1
                );
            if (h.Contains("ok"))
            {
                //  h = GetInterveningString(h, "ac=credit&op=exchange', '", "',");
            }
            return h;
        }

        internal static string GetOnlineMalls(int type)//拉取结晶商店失败提示
        {
            string url;
            if (type == 0)
            {
                url = "http://*************/forum.php?mod=viewthread&tid=752";//积分商店
            }
            else if (type == 1)
            {
                url = "http://*************/forum.php?mod=viewthread&tid=751";//结晶商店
            }
            else if (type == 2)
            {
                url = "http://*************/forum.php?mod=viewthread&tid=2107";//结晶商店
            }
            else
            {
                return null;
            }

            string h = new ConvertJson().GetWeb(url);
            if (string.IsNullOrEmpty(h))
            {
                return "出现错误：网络异常，无法获取到商店信息。";
            }
            if (h.IndexOf("抱歉，本帖要求阅读权限高于", StringComparison.Ordinal) != -1)
            {
                return "出现错误：没有登录或用户组权限过低。";
            }
            h = GetInterveningString(h, "<font color=\"#ffffff\">\\*", "\\*</font>");

            string[] cfg = h.Split('|');

            if (cfg.Length != 2 || Convert.ToInt32(cfg[0]) > Convert.ToInt32(DataProcess.Version))
            {
                return "获取商店信息失败,可能是以下问题：\r\n①游戏版本过低，无法获取商店信息。\r\n②月卡、年卡过期,请上论坛切换为普通用户组。\r\n③账号没有访问论坛的权限。";
            }

            h = SkRC4.DES.DecryptRC4(cfg[1], new DataProcess().GetKey(1));

            return h;
        }

        internal static bool UpdateFromHash()
        {
            string h = new ConvertJson().GetWeb("http://*************/forum.php?mod=viewthread&tid=160");
            h = GetInterveningString(h, " name=\"formhash\" value=\"", "\"");
            //Console.WriteLine(h);
            if (!string.IsNullOrEmpty(h))
            {
                FormHash = h;
                return true;
            }
            return false;
        }
        internal static string GetInterveningString(string str, string start, string end)
        {
            try
            {
                //开始字符串 
                string s1 = start;
                //结束字符串 
                string s2 = end;
                Regex rg = new Regex("(?<=(" + s1 + "))[.\\s\\S]*?(?=(" + s2 + "))", RegexOptions.Multiline | RegexOptions.Singleline);
                return rg.Match(str).Value;
            }
            catch
            {
                return "";
            }
        }


    }
}

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Timer = System.Windows.Forms.Timer;

namespace Shikong.Pokemon2.PCG
{
    public class FallingLeaves : Control
    {
        private readonly List<Leaf> leaves = new List<Leaf>();
        private readonly Random random = new Random();
        private readonly Timer animationTimer;
        private const int LEAF_COUNT = 15; // 减少落叶数量
        private const int LEAF_SIZE = 15; // 减小叶子大小
        private Image leafImage;

        public FallingLeaves()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor |
                    ControlStyles.OptimizedDoubleBuffer |
                    ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.UserPaint, true);

            BackColor = Color.Transparent;
            
            // 使用Windows.Forms.Timer代替System.Timers.Timer
            animationTimer = new Timer();
            animationTimer.Interval = 50;
            animationTimer.Tick += AnimationTimer_Tick;
            
            // 加载叶子图片（您需要准备一个叶子的PNG图片）
            try
            {
                leafImage = Image.FromFile("PageMain\\Content\\resources\\styles\\images\\leaf.png");
            }
            catch
            {
                // 如果图片加载失败，使用默认的椭圆形状
                leafImage = null;
            }

            // 初始化落叶
            for (int i = 0; i < LEAF_COUNT; i++)
            {
                leaves.Add(CreateLeaf());
            }

            this.Resize += FallingLeaves_Resize;
            animationTimer.Start();
        }

        private void FallingLeaves_Resize(object sender, EventArgs e)
        {
            // 当控件大小改变时，重新初始化落叶位置
            foreach (var leaf in leaves)
            {
                leaf.X = random.Next(0, Width);
                leaf.Y = random.Next(-Height, 0);
            }
        }

        private Leaf CreateLeaf()
        {
            return new Leaf
            {
                X = random.Next(0, Width),
                Y = random.Next(-Height, 0),
                Speed = random.Next(1, 3), // 降低速度
                Rotation = random.Next(0, 360),
                RotationSpeed = random.Next(-1, 2),
                Size = random.Next(LEAF_SIZE - 5, LEAF_SIZE + 5),
                Color = GetRandomLeafColor(),
                Alpha = random.Next(100, 200) // 添加透明度
            };
        }

        private Color GetRandomLeafColor()
        {
            // 修改为更适合游戏风格的颜色
            Color[] colors = {
                Color.FromArgb(255, 230, 190),  // 浅金色
                Color.FromArgb(255, 200, 160),  // 浅橙色
                Color.FromArgb(255, 180, 140),  // 珊瑚色
                Color.FromArgb(255, 160, 120),  // 浅棕色
                Color.FromArgb(255, 140, 100)   // 米色
            };
            return colors[random.Next(colors.Length)];
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            bool needInvalidate = false;
            foreach (var leaf in leaves)
            {
                // 更新叶子位置
                leaf.Y += leaf.Speed;
                leaf.X += (float)Math.Sin(leaf.Y / 50) * 1; // 减小摆动幅度
                leaf.Rotation += leaf.RotationSpeed;

                // 如果叶子落到底部，重新从顶部开始
                if (leaf.Y > Height)
                {
                    leaf.Y = -LEAF_SIZE;
                    leaf.X = random.Next(0, Width);
                    leaf.Speed = random.Next(1, 3);
                    leaf.Rotation = random.Next(0, 360);
                    leaf.RotationSpeed = random.Next(-1, 2);
                    leaf.Color = GetRandomLeafColor();
                    leaf.Alpha = random.Next(100, 200);
                }
                needInvalidate = true;
            }

            if (needInvalidate)
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;

            foreach (var leaf in leaves)
            {
                using (var brush = new SolidBrush(Color.FromArgb(leaf.Alpha, leaf.Color)))
                {
                    e.Graphics.TranslateTransform(leaf.X, leaf.Y);
                    e.Graphics.RotateTransform(leaf.Rotation);
                    
                    if (leafImage != null)
                    {
                        // 如果有图片，绘制图片
                        e.Graphics.DrawImage(leafImage, -leaf.Size / 2, -leaf.Size / 2, leaf.Size, leaf.Size);
                    }
                    else
                    {
                        // 否则绘制椭圆
                        e.Graphics.FillEllipse(brush, -leaf.Size / 2, -leaf.Size / 2, leaf.Size, leaf.Size);
                    }
                    
                    e.Graphics.ResetTransform();
                }
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                animationTimer?.Dispose();
                leafImage?.Dispose();
            }
            base.Dispose(disposing);
        }

        private class Leaf
        {
            public float X { get; set; }
            public float Y { get; set; }
            public float Speed { get; set; }
            public float Rotation { get; set; }
            public float RotationSpeed { get; set; }
            public float Size { get; set; }
            public Color Color { get; set; }
            public int Alpha { get; set; }
        }
    }
} 
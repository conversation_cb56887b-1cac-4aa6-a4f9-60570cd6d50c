能量汇聚功能集成说明
===================

为了完成能量汇聚功能的集成，您需要在Form1.cs的recv函数中添加以下代码：

1. 找到Form1.cs文件中的recv函数（大约在第2215行）

2. 在recv函数的早期部分（建议在第一个if语句之前）添加以下代码：

    // 处理能量汇聚相关命令
    if (ProcessEnergyGatherCommand(s))
    {
        return;
    }

3. 具体位置建议：在以下代码之后：

    if (s == null)
    {
        return;
    }

4. 完整的代码示例：

    public void recv(string s, string ys = "黑")
    {
        if (s == null)
        {
            return;
        }

        // 处理能量汇聚相关命令
        if (ProcessEnergyGatherCommand(s))
        {
            return;
        }

        // 后续的原有代码...
        if (DataProcess.AdminMode == 1)
        {
            // ...
        }

这样，当用户输入以下命令时，系统会正确处理：
- /神圣结界 或 /结界等级 - 查看当前神圣结界等级和经验
- /使用聚灵晶石 数量 - 使用指定数量的聚灵晶石
- /突破帮助 或 /成长突破 - 查看成长突破系统帮助

注意：请确保在添加代码时保持正确的缩进和格式。 
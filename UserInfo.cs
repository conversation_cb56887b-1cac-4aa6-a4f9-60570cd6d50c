﻿using Shikong.Pokemon2.PCG.占卜屋;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Shikong.Pokemon2.PCG
{
    public class UserInfo
    {
        public String NextBattle { get; set; }
        public List<String> openMaps { get; set; }//已开启地图
        public string password;
        public string 名字 { get; set; }

        public string sex { get; set; }
        [DefaultValue(0)]
        public string vip { get; set; }
        [DefaultValue(false)]
        public bool 至尊VIP { get; set; }
        public bool 星辰VIP { get; set; }//激活星辰VIP必激活至尊VIP，不需要额外写代码
        [DefaultValue(0)]
        public string VIP积分 { get; set; }


        [DefaultValue(0)] private string _金币;
        public string 金币
        {
            get => _金币;
            set
            {
                if (星辰VIP && Convert.ToInt64(value) > 15000000000 + new DataProcess().ghb(5))
                {
                    value = (15000000000 + new DataProcess().ghb(5)).ToString();
                }
                else if (至尊VIP && !星辰VIP && Convert.ToInt64(value) > 10000000000 + new DataProcess().ghb(5))
                {
                    value = (10000000000 + new DataProcess().ghb(5)).ToString();
                }
                else if (!星辰VIP && !至尊VIP && Convert.ToInt64(value) > 5000000000 + new DataProcess().ghb(5))
                {
                    value = (5000000000 + new DataProcess().ghb(5)).ToString();
                }
                _金币 = value;
            }
        }

        [DefaultValue(0)] private string _元宝;
        public string 元宝
        {
            get => _元宝;
            set {

                if (星辰VIP && Convert.ToInt32(value) > 60000000 + new DataProcess().ghb(7))
                {
                    value = (60000000 + new DataProcess().ghb(7)).ToString();
                }
                else if (至尊VIP && !星辰VIP && Convert.ToInt32(value) > 40000000 + new DataProcess().ghb(7))
                {
                    value = (40000000 + new DataProcess().ghb(7)).ToString();
                }
                else if (!星辰VIP && !至尊VIP && Convert.ToInt32(value) > 20000000 + new DataProcess().ghb(7))
                {
                    value = (20000000 + new DataProcess().ghb(7)).ToString();
                }
                _元宝 = value;
            }
        }
        public String 魂宠;
        [DefaultValue(0)] private string _水晶;
        public string 水晶
        {
            get => _水晶;
            set
            {
                if (星辰VIP && Convert.ToInt32(value) > 60000000 + new DataProcess().ghb(8))
                {
                    value = (60000000 + new DataProcess().ghb(8)).ToString();
                }
                else if (至尊VIP && !星辰VIP && Convert.ToInt32(value) > 40000000 + new DataProcess().ghb(8))
                {
                    value = (40000000 + new DataProcess().ghb(8)).ToString();
                }
                else if (!星辰VIP && !至尊VIP && Convert.ToInt32(value) > 20000000 + new DataProcess().ghb(8))
                {
                    value = (20000000 + new DataProcess().ghb(8)).ToString();
                }
                _水晶 = value;
            }
        }
        public string 累计消耗结晶 { get; set; } = "0";
        [DefaultValue(0)] private string _自动战斗次数;

        public string 自动战斗次数
        {
            get => _自动战斗次数;
            set
            {
                //if (Convert.ToInt32(value) > 1000000)
                //{
                //    value = "1000000";
                //}
                if (星辰VIP && Convert.ToInt32(value) > 10000000)
                {
                    value = "10000000";
                }
                else if (至尊VIP && !星辰VIP && Convert.ToInt32(value) > 5000000)
                {
                    value = "5000000";
                }
                else if (!星辰VIP && !至尊VIP && Convert.ToInt32(value) > 1000000)
                {
                    value = "1000000";
                }
                _自动战斗次数 = value;
            }
        }
        public int hela { get; set; } = 0;//存放活动地图击杀数，每次更新清0
        public int iceland { get; set; } = 0;//存放活动地图击杀数，不清空
        [DefaultValue(0)] private string _autoExp;

        public string AutoExp//普通宠物自动涅槃经验
        {
            get => _autoExp;
            set
            {
                if (Convert.ToInt64(value) > 5000000000000000000)
                {
                    value = "5000000000000000000";
                }
                _autoExp = value;
            }

        }
        [DefaultValue(0)] private string _autoExp2;
        public string AutoExp2//巫族宠物自动涅槃经验
        {
            get => _autoExp2;
            set
            {
                if (Convert.ToInt64(value) > 5000000000000000000)
                {
                    value = "/*5000000000000000000*/";
                }
                _autoExp2 = value;
            }

        }
        public string 论坛ID { get; set; }
        public string 称号 { get; set; }
        public string 道具容量 { get; set; }
        public string 牧场容量 { get; set; }
        //public String 战斗时间 { get; set; }
        //public string 创世礼包领取 { get; set; }
        [DefaultValue(0)]
        public string 每日礼包时间 { get; set; }
        public string 每日福利时间 { get; set; }
        //public string a { get; set; }
        public string 条款 { get; set; }
        public string 宠物1 { get; set; }
        public string 宠物2 { get; set; }
        public string 宠物3 { get; set; }
        public string 主宠物 { get; set; }
        [DefaultValue(0)]
        public string 威望 { get; set; }
        [DefaultValue(0)]
        public string 刷怪数 { get; set; }
        public string 每日刷怪数重置时间 { get; set; }
        public string 每日刷怪数 { get; set; }
        //public String 总刷怪数 { get; set; }
        //public String 已抽取cc { get; set; }
        [DefaultValue(0)]
        public string b { get; set; }
        //public String d { get; set; }
        public string 版本号 { get; set; }
        public string 小版本号 { get; set; }
        public string NB1 { get; set; }
        public string NB2 { get; set; }
        public string NB3 { get; set; }
        [DefaultValue(0)]
        public string 支持次数 { get; set; }
        [DefaultValue(0)]
        public string 支持时间 { get; set; }
        [DefaultValue(0)]
        public string 时之券 { get; set; }

        //public string 年月卡 { get; set; }

        public string AutoTime { get; set; }//自动合宠次数

        public string TaskHelper { get; set; }

        public string 地狱层数 { get; set; }

        public string TTT { get; set; }

        public string 宠物数量 => DataProcess.PetList.Count.ToString();

        public string 主宠名字
        {
            get
            {
                if (主宠物 == null)
                {
                    return null;
                }
                PetInfo 宠物情况 = new DataProcess().ReadAppointedPet(主宠物, DataProcess.PetList);
                if (宠物情况 == null)
                {
                    return null;
                }
                string 宠物 = 宠物情况.宠物名字;
                return 宠物;
            }
            //set { }
        }

        public string SID { get; set; } //存档唯一标识码
        public string 测试版本 { get; set; } = "1";
        public string 注册时间 { get; set; }

        public List<String> CardList { get; set; }//占卜屋拥有的卡牌ID
        public List<String> 商店限购 { get; set; }//限购列表  道具ID,已购买次数,货币类型

        public Dictionary<String, hqinfo> 魂器列表 { get; set; } = new Dictionary<string, hqinfo>();
        public longzhuInfo 龙珠信息 { get; set; } = null;
        public Dictionary<String, int> 神兵列表 { get; set; } = new Dictionary<string, int>();
        public List<string> 皮肤列表 { get; set; } = new List<string>();
        /// <summary>
        /// 额外的页数信息（所有功能默认为1页）
        /// 键值：
        /// 皮肤、神兵、魂器
        /// </summary>
        public Dictionary<String, int> 解锁页信息 { get; set; } = new Dictionary<string, int>();

        public double[] 龙珠经验随机组 { get; set; }
        public int 龙珠随机下标 { get; set; }
        public string 佩戴皮肤 { get; set; }

        // 成长突破相关属性（用户级别）
        /// <summary>
        /// 神圣结界等级（能量汇聚等级）
        /// </summary>
        [DefaultValue("0")]
        public string 神圣结界等级 { get; set; }

        /// <summary>
        /// 神圣结界当前经验
        /// </summary>
        [DefaultValue("0")]
        public string 神圣结界经验 { get; set; }
    }

    public class longzhuInfo
    {
        /// <summary>
        /// 当前经验
        /// </summary>
        public double exp;
        /// <summary>
        /// 龙珠的昵称
        /// </summary>
        public String name;
    }

    public class hqinfo
    {
        /// <summary>
        /// 等级决定了洗出的词条的属性上限
        /// </summary>
        public int 等级 { get; set; }
        /// <summary>
        /// 品级决定了能洗出哪些词条
        /// 0 普通
        /// 1 精良
        /// 2 优秀
        /// 3 稀有
        /// 4 史诗
        /// 5 传说
        /// </summary>
        public int 转灵品级 { get; set; }
        /// <summary>
        /// 金、木、水、火、土、暗
        /// </summary>
        public Dictionary<String, int> 元素等级 { get; set; }
        public Dictionary<String, double> 词条{ get; set; }
        public Dictionary<String, double> 待保留词条 { get; set; }
        public double 洗练次数 { get; set; }
        public double 转灵次数 { get; set; }
      
    }

}

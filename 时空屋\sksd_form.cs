﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.时空屋
{
    [PermissionSet(SecurityAction.Demand, Name = "FullTrust")]
    [ComVisible(true)] //com+可见
    public class sksd_form
    {
        public static void load(int page, String name)
        {
            //var e = DataProcess.PP_List.Where(C => C.道具位置 == "1");
            //if (name != null && name != "")
            //{
            //    e = e.Where(C => C.道具名字.Contains(name));
            //}
            //List<PropInfo> baglist = e.Where(C => C.道具位置 == "1").Skip(page * 50).Take(50).ToList();
            //string json = "";
            //foreach (var b in baglist)
            //{
            //    json += b.道具名字 + "," + b.道具图标 + "," + b.道具序号 + "," + b.道具类型ID + "," + b.道具数量 + "|";
            //}
            //DataProcess.GameForm.webBrowser1.Document.InvokeScript("loadProp", new object[] { json });
            //json = new DataProcess().ReadUserInfo().道具容量;
            //DataProcess.GameForm.webBrowser1.Document.InvokeScript("showUserInfo", new object[] { json, e.Count() });
        }
        public String getsjson()
        {
            String json = JsonConvert.SerializeObject(shenbing.GetShenbings());
            return json;
        }

    }
}
